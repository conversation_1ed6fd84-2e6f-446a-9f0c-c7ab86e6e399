import { SignInResult } from "../result/SignInResult.js";
import { SignInWithContinuationTokenInputs } from "../../../CustomAuthActionInputs.js";
import { SignInContinuationStateParameters } from "./SignInStateParameters.js";
import { SignInState } from "./SignInState.js";
export declare class SignInContinuationState extends SignInState<SignInContinuationStateParameters> {
    /**
     * Initiates the sign-in flow with continuation token.
     * @param {SignInWithContinuationTokenInputs} signInWithContinuationTokenInputs - The result of the operation.
     * @returns {Promise<SignInResult>} The result of the operation.
     */
    signIn(signInWithContinuationTokenInputs?: SignInWithContinuationTokenInputs): Promise<SignInResult>;
}
//# sourceMappingURL=SignInContinuationState.d.ts.map