import { SignInSubmitPasswordError } from "../error_type/SignInError.js";
import { SignInCompletedState } from "../state/SignInCompletedState.js";
import { SignInFailedState } from "../state/SignInFailedState.js";
import { SignInSubmitCredentialResult } from "./SignInSubmitCredentialResult.js";
export declare class SignInSubmitPasswordResult extends SignInSubmitCredentialResult<SignInSubmitPasswordError> {
    static createWithError(error: unknown): SignInSubmitPasswordResult;
    /**
     * Checks if the result is in a failed state.
     */
    isFailed(): this is SignInSubmitPasswordResult & {
        state: SignInFailedState;
    };
    /**
     * Checks if the result is in a completed state.
     */
    isCompleted(): this is SignInSubmitPasswordResult & {
        state: SignInCompletedState;
    };
}
//# sourceMappingURL=SignInSubmitPasswordResult.d.ts.map