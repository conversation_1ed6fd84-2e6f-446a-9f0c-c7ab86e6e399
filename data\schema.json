{"databaseName": "universitydb", "databaseType": "mysql", "tables": [{"name": "academicyear", "columns": [{"name": "AcademicYearID", "type": "int", "nullable": false, "defaultValue": null, "isPrimaryKey": true, "isForeignKey": false}, {"name": "<PERSON><PERSON><PERSON>", "type": "<PERSON><PERSON><PERSON>(20)", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 20}, {"name": "StartDate", "type": "date", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false}, {"name": "EndDate", "type": "date", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false}], "foreignKeys": [], "indexes": [{"name": "PRIMARY", "columns": ["AcademicYearID"], "isUnique": true, "isPrimary": true}], "primaryKeys": ["AcademicYearID"], "rowCount": 4}, {"name": "attendance", "columns": [{"name": "AttendanceID", "type": "int", "nullable": false, "defaultValue": null, "isPrimaryKey": true, "isForeignKey": false}, {"name": "LectureID", "type": "int", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": true}, {"name": "StudentID", "type": "int", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": true}, {"name": "StatusID", "type": "int", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": true}, {"name": "Notes", "type": "text", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false}], "foreignKeys": [{"columnName": "LectureID", "referencedTable": "lectures", "referencedColumn": "LectureID", "constraintName": "attendance_ibfk_1"}, {"columnName": "StudentID", "referencedTable": "students", "referencedColumn": "StudentID", "constraintName": "attendance_ibfk_2"}, {"columnName": "StatusID", "referencedTable": "attendancestatus", "referencedColumn": "StatusID", "constraintName": "attendance_ibfk_3"}], "indexes": [{"name": "PRIMARY", "columns": ["AttendanceID"], "isUnique": true, "isPrimary": true}, {"name": "LectureID", "columns": ["LectureID"], "isUnique": false, "isPrimary": false}, {"name": "StudentID", "columns": ["StudentID"], "isUnique": false, "isPrimary": false}, {"name": "StatusID", "columns": ["StatusID"], "isUnique": false, "isPrimary": false}], "primaryKeys": ["AttendanceID"], "rowCount": 1}, {"name": "attendancestatus", "columns": [{"name": "StatusID", "type": "int", "nullable": false, "defaultValue": null, "isPrimaryKey": true, "isForeignKey": false}, {"name": "StatusName", "type": "<PERSON><PERSON><PERSON>(20)", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 20}], "foreignKeys": [], "indexes": [{"name": "PRIMARY", "columns": ["StatusID"], "isUnique": true, "isPrimary": true}], "primaryKeys": ["StatusID"], "rowCount": 2}, {"name": "colleges", "columns": [{"name": "CollegeID", "type": "int", "nullable": false, "defaultValue": null, "isPrimaryKey": true, "isForeignKey": false}, {"name": "CollegeName", "type": "<PERSON><PERSON><PERSON>(100)", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 100}, {"name": "CollegeCode", "type": "<PERSON><PERSON><PERSON>(10)", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 10}, {"name": "Description", "type": "text", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false}], "foreignKeys": [], "indexes": [{"name": "PRIMARY", "columns": ["CollegeID"], "isUnique": true, "isPrimary": true}, {"name": "CollegeCode", "columns": ["CollegeCode"], "isUnique": true, "isPrimary": false}], "primaryKeys": ["CollegeID"], "rowCount": 4}, {"name": "departments", "columns": [{"name": "DepartmentID", "type": "int", "nullable": false, "defaultValue": null, "isPrimaryKey": true, "isForeignKey": false}, {"name": "DepartmentName", "type": "<PERSON><PERSON><PERSON>(100)", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 100}, {"name": "DepartmentCode", "type": "<PERSON><PERSON><PERSON>(10)", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 10}, {"name": "CollegeID", "type": "int", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": true}], "foreignKeys": [{"columnName": "CollegeID", "referencedTable": "colleges", "referencedColumn": "CollegeID", "constraintName": "departments_ibfk_1"}], "indexes": [{"name": "PRIMARY", "columns": ["DepartmentID"], "isUnique": true, "isPrimary": true}, {"name": "DepartmentCode", "columns": ["DepartmentCode"], "isUnique": true, "isPrimary": false}, {"name": "CollegeID", "columns": ["CollegeID"], "isUnique": false, "isPrimary": false}], "primaryKeys": ["DepartmentID"], "rowCount": 5}, {"name": "grades", "columns": [{"name": "GradeID", "type": "int", "nullable": false, "defaultValue": null, "isPrimaryKey": true, "isForeignKey": false}, {"name": "StudentID", "type": "int", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": true}, {"name": "SubjectID", "type": "int", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": true}, {"name": "SemesterID", "type": "int", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": true}, {"name": "Grade", "type": "decimal(5,2)", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "precision": 5, "scale": 2}, {"name": "GradeDate", "type": "date", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false}], "foreignKeys": [{"columnName": "StudentID", "referencedTable": "students", "referencedColumn": "StudentID", "constraintName": "grades_ibfk_1"}, {"columnName": "SubjectID", "referencedTable": "subjects", "referencedColumn": "SubjectID", "constraintName": "grades_ibfk_2"}, {"columnName": "SemesterID", "referencedTable": "semesters", "referencedColumn": "SemesterID", "constraintName": "grades_ibfk_3"}], "indexes": [{"name": "PRIMARY", "columns": ["GradeID"], "isUnique": true, "isPrimary": true}, {"name": "StudentID", "columns": ["StudentID"], "isUnique": false, "isPrimary": false}, {"name": "SubjectID", "columns": ["SubjectID"], "isUnique": false, "isPrimary": false}, {"name": "SemesterID", "columns": ["SemesterID"], "isUnique": false, "isPrimary": false}], "primaryKeys": ["GradeID"], "rowCount": 0}, {"name": "halls", "columns": [{"name": "HallID", "type": "int", "nullable": false, "defaultValue": null, "isPrimaryKey": true, "isForeignKey": false}, {"name": "Building", "type": "<PERSON><PERSON><PERSON>(50)", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 50}, {"name": "Hall<PERSON><PERSON>ber", "type": "int", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false}], "foreignKeys": [], "indexes": [{"name": "PRIMARY", "columns": ["HallID"], "isUnique": true, "isPrimary": true}], "primaryKeys": ["HallID"], "rowCount": 9}, {"name": "lectures", "columns": [{"name": "LectureID", "type": "int", "nullable": false, "defaultValue": null, "isPrimaryKey": true, "isForeignKey": false}, {"name": "LectureDate", "type": "date", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false}, {"name": "LectureTime", "type": "time", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false}, {"name": "LectureTitle", "type": "<PERSON><PERSON><PERSON>(100)", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 100}, {"name": "SubjectID", "type": "int", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": true}, {"name": "ProfessorID", "type": "int", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": true}, {"name": "SemesterID", "type": "int", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": true}, {"name": "HallID", "type": "int", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": true}, {"name": "EndTime", "type": "time", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false}], "foreignKeys": [{"columnName": "HallID", "referencedTable": "halls", "referencedColumn": "HallID", "constraintName": "fk_hall"}, {"columnName": "SubjectID", "referencedTable": "subjects", "referencedColumn": "SubjectID", "constraintName": "lectures_ibfk_1"}, {"columnName": "ProfessorID", "referencedTable": "professors", "referencedColumn": "ProfessorID", "constraintName": "lectures_ibfk_2"}, {"columnName": "SemesterID", "referencedTable": "semesters", "referencedColumn": "SemesterID", "constraintName": "lectures_ibfk_3"}], "indexes": [{"name": "PRIMARY", "columns": ["LectureID"], "isUnique": true, "isPrimary": true}, {"name": "SubjectID", "columns": ["SubjectID"], "isUnique": false, "isPrimary": false}, {"name": "ProfessorID", "columns": ["ProfessorID"], "isUnique": false, "isPrimary": false}, {"name": "SemesterID", "columns": ["SemesterID"], "isUnique": false, "isPrimary": false}, {"name": "fk_hall", "columns": ["HallID"], "isUnique": false, "isPrimary": false}], "primaryKeys": ["LectureID"], "rowCount": 26}, {"name": "levels", "columns": [{"name": "LevelID", "type": "int", "nullable": false, "defaultValue": null, "isPrimaryKey": true, "isForeignKey": false}, {"name": "LevelName", "type": "<PERSON><PERSON><PERSON>(50)", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 50}, {"name": "DepartmentID", "type": "int", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": true}], "foreignKeys": [{"columnName": "DepartmentID", "referencedTable": "departments", "referencedColumn": "DepartmentID", "constraintName": "levels_ibfk_1"}], "indexes": [{"name": "PRIMARY", "columns": ["LevelID"], "isUnique": true, "isPrimary": true}, {"name": "DepartmentID", "columns": ["DepartmentID"], "isUnique": false, "isPrimary": false}], "primaryKeys": ["LevelID"], "rowCount": 21}, {"name": "professors", "columns": [{"name": "ProfessorID", "type": "int", "nullable": false, "defaultValue": null, "isPrimaryKey": true, "isForeignKey": false}, {"name": "FirstName", "type": "<PERSON><PERSON><PERSON>(50)", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 50}, {"name": "LastName", "type": "<PERSON><PERSON><PERSON>(50)", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 50}, {"name": "Email", "type": "<PERSON><PERSON><PERSON>(100)", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 100}, {"name": "Phone", "type": "<PERSON><PERSON><PERSON>(15)", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 15}, {"name": "DepartmentID", "type": "int", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": true}], "foreignKeys": [{"columnName": "DepartmentID", "referencedTable": "departments", "referencedColumn": "DepartmentID", "constraintName": "professors_ibfk_1"}], "indexes": [{"name": "PRIMARY", "columns": ["ProfessorID"], "isUnique": true, "isPrimary": true}, {"name": "Email", "columns": ["Email"], "isUnique": true, "isPrimary": false}, {"name": "DepartmentID", "columns": ["DepartmentID"], "isUnique": false, "isPrimary": false}], "primaryKeys": ["ProfessorID"], "rowCount": 8}, {"name": "repeatingstudents", "columns": [{"name": "RepeatingStudentID", "type": "int", "nullable": false, "defaultValue": null, "isPrimaryKey": true, "isForeignKey": false}, {"name": "StudentID", "type": "int", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": true}, {"name": "SubjectID", "type": "int", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": true}, {"name": "Reason", "type": "<PERSON><PERSON><PERSON>(20)", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 20}, {"name": "RepeatingSemesterID", "type": "int", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": true}], "foreignKeys": [{"columnName": "StudentID", "referencedTable": "students", "referencedColumn": "StudentID", "constraintName": "repeatingstudents_ibfk_1"}, {"columnName": "SubjectID", "referencedTable": "subjects", "referencedColumn": "SubjectID", "constraintName": "repeatingstudents_ibfk_2"}, {"columnName": "RepeatingSemesterID", "referencedTable": "semesters", "referencedColumn": "SemesterID", "constraintName": "repeatingstudents_ibfk_3"}], "indexes": [{"name": "PRIMARY", "columns": ["RepeatingStudentID"], "isUnique": true, "isPrimary": true}, {"name": "SubjectID", "columns": ["SubjectID"], "isUnique": false, "isPrimary": false}, {"name": "RepeatingSemesterID", "columns": ["RepeatingSemesterID"], "isUnique": false, "isPrimary": false}, {"name": "repeatingstudents_ibfk_1", "columns": ["StudentID"], "isUnique": false, "isPrimary": false}], "primaryKeys": ["RepeatingStudentID"], "rowCount": 2}, {"name": "semesters", "columns": [{"name": "SemesterID", "type": "int", "nullable": false, "defaultValue": null, "isPrimaryKey": true, "isForeignKey": false}, {"name": "SemesterName", "type": "<PERSON><PERSON><PERSON>(50)", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 50}, {"name": "StartDate", "type": "date", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false}, {"name": "EndDate", "type": "date", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false}, {"name": "AcademicYearID", "type": "int", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": true}, {"name": "LevelID", "type": "int", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": true}], "foreignKeys": [{"columnName": "AcademicYearID", "referencedTable": "academicyear", "referencedColumn": "AcademicYearID", "constraintName": "semesters_ibfk_1"}, {"columnName": "LevelID", "referencedTable": "levels", "referencedColumn": "LevelID", "constraintName": "semesters_ibfk_2"}], "indexes": [{"name": "PRIMARY", "columns": ["SemesterID"], "isUnique": true, "isPrimary": true}, {"name": "AcademicYearID", "columns": ["AcademicYearID"], "isUnique": false, "isPrimary": false}, {"name": "LevelID", "columns": ["LevelID"], "isUnique": false, "isPrimary": false}], "primaryKeys": ["SemesterID"], "rowCount": 5}, {"name": "student_status", "columns": [{"name": "id", "type": "int", "nullable": false, "defaultValue": null, "isPrimaryKey": true, "isForeignKey": false}, {"name": "student_id", "type": "int", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": true}, {"name": "subject_id", "type": "int", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": true}, {"name": "status", "type": "enum('منتظم','انذار','محروم','تعهد')", "nullable": true, "defaultValue": "منتظم", "isPrimaryKey": false, "isForeignKey": false}], "foreignKeys": [{"columnName": "student_id", "referencedTable": "students", "referencedColumn": "StudentID", "constraintName": "student_status_ibfk_1"}, {"columnName": "subject_id", "referencedTable": "subjects", "referencedColumn": "SubjectID", "constraintName": "student_status_ibfk_2"}], "indexes": [{"name": "PRIMARY", "columns": ["id"], "isUnique": true, "isPrimary": true}, {"name": "student_id", "columns": ["student_id"], "isUnique": false, "isPrimary": false}, {"name": "subject_id", "columns": ["subject_id"], "isUnique": false, "isPrimary": false}], "primaryKeys": ["id"], "rowCount": 0}, {"name": "students", "columns": [{"name": "StudentID", "type": "int", "nullable": false, "defaultValue": null, "isPrimaryKey": true, "isForeignKey": false}, {"name": "AcademicNumber", "type": "<PERSON><PERSON><PERSON>(50)", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 50}, {"name": "FirstName", "type": "<PERSON><PERSON><PERSON>(50)", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 50}, {"name": "LastName", "type": "<PERSON><PERSON><PERSON>(50)", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 50}, {"name": "Phone", "type": "<PERSON><PERSON><PERSON>(15)", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 15}, {"name": "Email", "type": "<PERSON><PERSON><PERSON>(50)", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 50}, {"name": "EnrollmentDate", "type": "date", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false}, {"name": "DepartmentID", "type": "int", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": true}, {"name": "CurrentLevelID", "type": "int", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": true}], "foreignKeys": [{"columnName": "DepartmentID", "referencedTable": "departments", "referencedColumn": "DepartmentID", "constraintName": "students_ibfk_1"}, {"columnName": "CurrentLevelID", "referencedTable": "levels", "referencedColumn": "LevelID", "constraintName": "students_ibfk_2"}], "indexes": [{"name": "PRIMARY", "columns": ["StudentID"], "isUnique": true, "isPrimary": true}, {"name": "DepartmentID", "columns": ["DepartmentID"], "isUnique": false, "isPrimary": false}, {"name": "CurrentLevelID", "columns": ["CurrentLevelID"], "isUnique": false, "isPrimary": false}], "primaryKeys": ["StudentID"], "rowCount": 338}, {"name": "studentstatus", "columns": [{"name": "StatusID", "type": "int", "nullable": false, "defaultValue": null, "isPrimaryKey": true, "isForeignKey": false}, {"name": "StudentID", "type": "int", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": true}, {"name": "StatusType", "type": "<PERSON><PERSON><PERSON>(20)", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 20}, {"name": "StatusDate", "type": "date", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false}, {"name": "Reason", "type": "text", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false}], "foreignKeys": [{"columnName": "StudentID", "referencedTable": "students", "referencedColumn": "StudentID", "constraintName": "studentstatus_ibfk_1"}], "indexes": [{"name": "PRIMARY", "columns": ["StatusID"], "isUnique": true, "isPrimary": true}, {"name": "StudentID", "columns": ["StudentID"], "isUnique": false, "isPrimary": false}], "primaryKeys": ["StatusID"], "rowCount": 0}, {"name": "subjects", "columns": [{"name": "SubjectID", "type": "int", "nullable": false, "defaultValue": null, "isPrimaryKey": true, "isForeignKey": false}, {"name": "SubjectName", "type": "<PERSON><PERSON><PERSON>(100)", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 100}, {"name": "SubjectCode", "type": "<PERSON><PERSON><PERSON>(10)", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 10}, {"name": "Credits", "type": "int", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false}, {"name": "DepartmentID", "type": "int", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": true}, {"name": "LevelID", "type": "int", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": true}, {"name": "SemesterID", "type": "int", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": true}], "foreignKeys": [{"columnName": "DepartmentID", "referencedTable": "departments", "referencedColumn": "DepartmentID", "constraintName": "subjects_ibfk_1"}, {"columnName": "LevelID", "referencedTable": "levels", "referencedColumn": "LevelID", "constraintName": "subjects_ibfk_2"}, {"columnName": "SemesterID", "referencedTable": "semesters", "referencedColumn": "SemesterID", "constraintName": "subjects_ibfk_3"}], "indexes": [{"name": "PRIMARY", "columns": ["SubjectID"], "isUnique": true, "isPrimary": true}, {"name": "SubjectCode", "columns": ["SubjectCode"], "isUnique": true, "isPrimary": false}, {"name": "DepartmentID", "columns": ["DepartmentID"], "isUnique": false, "isPrimary": false}, {"name": "LevelID", "columns": ["LevelID"], "isUnique": false, "isPrimary": false}, {"name": "subjects_ibfk_3", "columns": ["SemesterID"], "isUnique": false, "isPrimary": false}], "primaryKeys": ["SubjectID"], "rowCount": 7}, {"name": "users", "columns": [{"name": "UserID", "type": "int", "nullable": false, "defaultValue": null, "isPrimaryKey": true, "isForeignKey": false}, {"name": "Username", "type": "<PERSON><PERSON><PERSON>(50)", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 50}, {"name": "Password", "type": "<PERSON><PERSON><PERSON>(255)", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 255}, {"name": "Role", "type": "enum('admin','professor')", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false}, {"name": "ProfessorID", "type": "int", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": true}, {"name": "StudentID", "type": "int", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": true}, {"name": "RegistrationDate", "type": "datetime", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false}], "foreignKeys": [{"columnName": "ProfessorID", "referencedTable": "professors", "referencedColumn": "ProfessorID", "constraintName": "users_ibfk_1"}, {"columnName": "StudentID", "referencedTable": "students", "referencedColumn": "StudentID", "constraintName": "users_ibfk_2"}], "indexes": [{"name": "PRIMARY", "columns": ["UserID"], "isUnique": true, "isPrimary": true}, {"name": "Username", "columns": ["Username"], "isUnique": true, "isPrimary": false}, {"name": "ProfessorID", "columns": ["ProfessorID"], "isUnique": false, "isPrimary": false}, {"name": "StudentID", "columns": ["StudentID"], "isUnique": false, "isPrimary": false}], "primaryKeys": ["UserID"], "rowCount": 5}], "relationships": [{"fromTable": "attendance", "fromColumn": "LectureID", "toTable": "lectures", "toColumn": "LectureID", "relationshipType": "one-to-many", "constraintName": "attendance_ibfk_1"}, {"fromTable": "attendance", "fromColumn": "StudentID", "toTable": "students", "toColumn": "StudentID", "relationshipType": "one-to-many", "constraintName": "attendance_ibfk_2"}, {"fromTable": "attendance", "fromColumn": "StatusID", "toTable": "attendancestatus", "toColumn": "StatusID", "relationshipType": "one-to-many", "constraintName": "attendance_ibfk_3"}, {"fromTable": "departments", "fromColumn": "CollegeID", "toTable": "colleges", "toColumn": "CollegeID", "relationshipType": "one-to-many", "constraintName": "departments_ibfk_1"}, {"fromTable": "grades", "fromColumn": "StudentID", "toTable": "students", "toColumn": "StudentID", "relationshipType": "one-to-many", "constraintName": "grades_ibfk_1"}, {"fromTable": "grades", "fromColumn": "SubjectID", "toTable": "subjects", "toColumn": "SubjectID", "relationshipType": "one-to-many", "constraintName": "grades_ibfk_2"}, {"fromTable": "grades", "fromColumn": "SemesterID", "toTable": "semesters", "toColumn": "SemesterID", "relationshipType": "one-to-many", "constraintName": "grades_ibfk_3"}, {"fromTable": "lectures", "fromColumn": "HallID", "toTable": "halls", "toColumn": "HallID", "relationshipType": "one-to-many", "constraintName": "fk_hall"}, {"fromTable": "lectures", "fromColumn": "SubjectID", "toTable": "subjects", "toColumn": "SubjectID", "relationshipType": "one-to-many", "constraintName": "lectures_ibfk_1"}, {"fromTable": "lectures", "fromColumn": "ProfessorID", "toTable": "professors", "toColumn": "ProfessorID", "relationshipType": "one-to-many", "constraintName": "lectures_ibfk_2"}, {"fromTable": "lectures", "fromColumn": "SemesterID", "toTable": "semesters", "toColumn": "SemesterID", "relationshipType": "one-to-many", "constraintName": "lectures_ibfk_3"}, {"fromTable": "levels", "fromColumn": "DepartmentID", "toTable": "departments", "toColumn": "DepartmentID", "relationshipType": "one-to-many", "constraintName": "levels_ibfk_1"}, {"fromTable": "professors", "fromColumn": "DepartmentID", "toTable": "departments", "toColumn": "DepartmentID", "relationshipType": "one-to-many", "constraintName": "professors_ibfk_1"}, {"fromTable": "repeatingstudents", "fromColumn": "StudentID", "toTable": "students", "toColumn": "StudentID", "relationshipType": "one-to-many", "constraintName": "repeatingstudents_ibfk_1"}, {"fromTable": "repeatingstudents", "fromColumn": "SubjectID", "toTable": "subjects", "toColumn": "SubjectID", "relationshipType": "one-to-many", "constraintName": "repeatingstudents_ibfk_2"}, {"fromTable": "repeatingstudents", "fromColumn": "RepeatingSemesterID", "toTable": "semesters", "toColumn": "SemesterID", "relationshipType": "one-to-many", "constraintName": "repeatingstudents_ibfk_3"}, {"fromTable": "semesters", "fromColumn": "AcademicYearID", "toTable": "academicyear", "toColumn": "AcademicYearID", "relationshipType": "one-to-many", "constraintName": "semesters_ibfk_1"}, {"fromTable": "semesters", "fromColumn": "LevelID", "toTable": "levels", "toColumn": "LevelID", "relationshipType": "one-to-many", "constraintName": "semesters_ibfk_2"}, {"fromTable": "student_status", "fromColumn": "student_id", "toTable": "students", "toColumn": "StudentID", "relationshipType": "one-to-many", "constraintName": "student_status_ibfk_1"}, {"fromTable": "student_status", "fromColumn": "subject_id", "toTable": "subjects", "toColumn": "SubjectID", "relationshipType": "one-to-many", "constraintName": "student_status_ibfk_2"}, {"fromTable": "students", "fromColumn": "DepartmentID", "toTable": "departments", "toColumn": "DepartmentID", "relationshipType": "one-to-many", "constraintName": "students_ibfk_1"}, {"fromTable": "students", "fromColumn": "CurrentLevelID", "toTable": "levels", "toColumn": "LevelID", "relationshipType": "one-to-many", "constraintName": "students_ibfk_2"}, {"fromTable": "studentstatus", "fromColumn": "StudentID", "toTable": "students", "toColumn": "StudentID", "relationshipType": "one-to-many", "constraintName": "studentstatus_ibfk_1"}, {"fromTable": "subjects", "fromColumn": "DepartmentID", "toTable": "departments", "toColumn": "DepartmentID", "relationshipType": "one-to-many", "constraintName": "subjects_ibfk_1"}, {"fromTable": "subjects", "fromColumn": "LevelID", "toTable": "levels", "toColumn": "LevelID", "relationshipType": "one-to-many", "constraintName": "subjects_ibfk_2"}, {"fromTable": "subjects", "fromColumn": "SemesterID", "toTable": "semesters", "toColumn": "SemesterID", "relationshipType": "one-to-many", "constraintName": "subjects_ibfk_3"}, {"fromTable": "users", "fromColumn": "ProfessorID", "toTable": "professors", "toColumn": "ProfessorID", "relationshipType": "one-to-many", "constraintName": "users_ibfk_1"}, {"fromTable": "users", "fromColumn": "StudentID", "toTable": "students", "toColumn": "StudentID", "relationshipType": "one-to-many", "constraintName": "users_ibfk_2"}], "extractedAt": "2025-07-18T01:55:36.642Z", "version": "1.0"}