import { SignUpSubmitPasswordResult } from "../result/SignUpSubmitPasswordResult.js";
import { SignUpState } from "./SignUpState.js";
import { SignUpPasswordRequiredStateParameters } from "./SignUpStateParameters.js";
export declare class SignUpPasswordRequiredState extends SignUpState<SignUpPasswordRequiredStateParameters> {
    /**
     * Submits a password for sign-up.
     * @param {string} password - The password to submit.
     * @returns {Promise<SignUpSubmitPasswordResult>} The result of the operation.
     */
    submitPassword(password: string): Promise<SignUpSubmitPasswordResult>;
}
//# sourceMappingURL=SignUpPasswordRequiredState.d.ts.map