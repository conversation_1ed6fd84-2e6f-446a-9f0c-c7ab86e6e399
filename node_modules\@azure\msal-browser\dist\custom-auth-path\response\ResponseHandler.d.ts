import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Lo<PERSON>, AuthorizeResponse } from "@azure/msal-common/browser";
import { InteractionType } from "../utils/BrowserConstants.js";
export declare function deserializeResponse(responseString: string, responseLocation: string, logger: Logger): AuthorizeResponse;
/**
 * Returns the interaction type that the response object belongs to
 */
export declare function validateInteractionType(response: AuthorizeResponse, browserCrypto: ICrypto, interactionType: InteractionType): void;
//# sourceMappingURL=ResponseHandler.d.ts.map