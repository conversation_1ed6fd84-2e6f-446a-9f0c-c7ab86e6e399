import { IWindowStorage } from "./IWindowStorage.js";
export declare class SessionStorage implements IWindowStorage<string> {
    constructor();
    initialize(): Promise<void>;
    getItem(key: string): string | null;
    getUserData(key: string): string | null;
    setItem(key: string, value: string): void;
    setUserData(key: string, value: string): Promise<void>;
    removeItem(key: string): void;
    getKeys(): string[];
    containsKey(key: string): boolean;
}
//# sourceMappingURL=SessionStorage.d.ts.map