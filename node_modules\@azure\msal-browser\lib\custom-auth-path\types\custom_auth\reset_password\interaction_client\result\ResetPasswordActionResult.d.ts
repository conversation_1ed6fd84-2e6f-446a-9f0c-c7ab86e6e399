interface ResetPasswordActionResult {
    correlationId: string;
    continuationToken: string;
}
export interface ResetPasswordCodeRequiredResult extends ResetPasswordActionResult {
    challengeChannel: string;
    challengeTargetLabel: string;
    codeLength: number;
    bindingMethod: string;
}
export type ResetPasswordPasswordRequiredResult = ResetPasswordActionResult;
export type ResetPasswordCompletedResult = ResetPasswordActionResult;
export {};
//# sourceMappingURL=ResetPasswordActionResult.d.ts.map